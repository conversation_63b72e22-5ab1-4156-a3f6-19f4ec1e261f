# dob_input.py

from datetime import datetime

def ask_for_dob():
    print("Enter your date of birth (format: YYYY-MM-DD):")
    dob_input = input("DOB: ")

    try:
        dob = datetime.strptime(dob_input, "%Y-%m-%d")
        print(f"✅ Your date of birth is: {dob.strftime('%B %d, %Y')}")
    except ValueError:
        print("❌ Invalid format! Please enter in YYYY-MM-DD format.")

if __name__ == "__main__":
    ask_for_dob()
